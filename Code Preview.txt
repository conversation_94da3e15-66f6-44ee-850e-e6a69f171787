<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Social Feed UI with Gemini Features</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
        }
        .lucide {
            width: 20px;
            height: 20px;
        }
        /* Simple spinner for loading states */
        .spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #4f46e5;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        /* Styles for the modal */
        #commentModal.hidden {
            display: none;
        }
    </style>
</head>
<body class="bg-slate-50 text-slate-800">

    <div id="app" class="min-h-screen">
        <!-- Top Navigation Bar -->
        <header class="bg-white/80 backdrop-blur-lg border-b border-slate-200 fixed top-0 left-0 right-0 z-20">
            <div class="container mx-auto px-4">
                <div class="flex justify-between items-center h-16">
                    <!-- Logo -->
                    <a href="#" class="flex items-center gap-2 text-xl font-bold text-slate-900">
                        <i data-lucide="layers-3"></i>
                        <span>Narrate</span>
                    </a>

                    <!-- Search Bar -->
                    <div class="hidden md:block w-full max-w-md">
                        <div class="relative">
                            <i data-lucide="search" class="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 lucide"></i>
                            <input type="text" placeholder="Search stories, people, or topics..." class="w-full bg-slate-100 border border-transparent rounded-full py-2 pl-10 pr-4 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition">
                        </div>
                    </div>

                    <!-- Header Actions -->
                    <div class="flex items-center gap-4">
                        <button class="hidden md:flex items-center gap-2 bg-indigo-600 text-white font-semibold px-4 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">
                            <i data-lucide="pen-square"></i>
                            <span>Write</span>
                        </button>
                        <button class="p-2 rounded-full hover:bg-slate-100 transition">
                            <i data-lucide="bell" class="text-slate-600"></i>
                        </button>
                        <button class="p-2 rounded-full hover:bg-slate-100 transition">
                            <i data-lucide="message-square" class="text-slate-600"></i>
                        </button>
                        <img src="https://placehold.co/40x40/E2E8F0/475569?text=JD" alt="User Avatar" class="w-10 h-10 rounded-full border-2 border-white cursor-pointer">
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Grid -->
        <main class="container mx-auto px-4 pt-24 pb-10">
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-8">

                <!-- Left Column (Profile & Navigation) -->
                <aside class="hidden lg:block lg:col-span-3 space-y-6">
                    <!-- Profile Card -->
                    <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <div class="flex flex-col items-center text-center">
                            <img src="https://placehold.co/80x80/E2E8F0/475569?text=JD" alt="John D" class="w-20 h-20 rounded-full mb-3 ring-4 ring-indigo-100">
                            <h2 class="text-xl font-bold text-slate-900">John D</h2>
                            <p class="text-sm text-slate-500 mt-1">As a black man who strides for always trying to do something great.</p>
                        </div>
                        <div class="grid grid-cols-3 gap-2 text-center my-5">
                            <div>
                                <p class="font-bold text-lg">12</p>
                                <p class="text-xs text-slate-500">Stories</p>
                            </div>
                            <div>
                                <p class="font-bold text-lg">1.2k</p>
                                <p class="text-xs text-slate-500">Followers</p>
                            </div>
                            <div>
                                <p class="font-bold text-lg">345</p>
                                <p class="text-xs text-slate-500">Following</p>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2 justify-center">
                            <span class="bg-indigo-100 text-indigo-700 text-xs font-semibold px-2.5 py-1 rounded-full">Storytelling</span>
                            <span class="bg-amber-100 text-amber-700 text-xs font-semibold px-2.5 py-1 rounded-full">Community</span>
                            <span class="bg-sky-100 text-sky-700 text-xs font-semibold px-2.5 py-1 rounded-full">Leadership</span>
                        </div>
                    </div>
                    <!-- Navigation Links -->
                    <div class="bg-white p-3 rounded-xl border border-slate-200 shadow-sm">
                        <nav class="space-y-1">
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg bg-indigo-50 text-indigo-700 font-semibold">
                                <i data-lucide="layout-grid"></i> Feed
                            </a>
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="compass"></i> Explore
                            </a>
                            <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="users"></i> Connect
                            </a>
                             <a href="#" class="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-slate-100 text-slate-600 font-medium">
                                <i data-lucide="bookmark"></i> Bookmarks
                            </a>
                        </nav>
                    </div>
                </aside>

                <!-- Center Column (Main Feed) -->
                <div class="lg:col-span-6 space-y-6">
                    <!-- Create Post -->
                    <div class="bg-white p-4 rounded-xl border border-slate-200 shadow-sm">
                        <div class="flex items-start gap-4">
                            <img src="https://placehold.co/40x40/E2E8F0/475569?text=JD" alt="User Avatar" class="w-10 h-10 rounded-full">
                            <div class="w-full space-y-3">
                                <input type="text" id="storyTitleInput" placeholder="Your story's title..." class="w-full border-slate-300 rounded-lg text-lg font-semibold placeholder-slate-400 focus:ring-indigo-500 focus:border-indigo-500">
                                <textarea id="storyContentInput" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2 placeholder-slate-400" rows="3" placeholder="What's your story today, John?"></textarea>
                                <div id="titleSuggestionsContainer" class="hidden space-y-2"></div>
                                <div class="flex justify-between items-center pt-3 border-t border-slate-200">
                                    <div class="flex items-center gap-4">
                                        <button class="text-slate-500 hover:text-indigo-600"><i data-lucide="image"></i></button>
                                        <button class="text-slate-500 hover:text-indigo-600"><i data-lucide="video"></i></button>
                                        <button id="suggestTitlesBtn" class="hidden items-center gap-1.5 text-sm font-semibold text-indigo-600 hover:text-indigo-800">✨ Suggest Titles</button>
                                    </div>
                                    <button class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Post</button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Story Card -->
                    <article class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm" data-story-title="My Journey to Building a Community" data-story-content="This is a test story to demonstrate the new layout. It's much cleaner and provides a better reading experience. The focus is on consistency and clarity, ensuring that every element has a purpose and contributes to a cohesive design.">
                        <div class="flex items-center mb-4">
                            <img src="https://placehold.co/48x48/E2E8F0/475569?text=JD" alt="John D" class="w-12 h-12 rounded-full">
                            <div class="ml-4">
                                <h3 class="font-bold text-slate-900">John D</h3>
                                <p class="text-sm text-slate-500">Just now &middot; <span class="font-semibold text-indigo-600">Personal Experience</span></p>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold mb-2">My Journey to Building a Community</h2>
                        <p class="text-slate-600 leading-relaxed">This is a test story to demonstrate the new layout. It's much cleaner and provides a better reading experience. The focus is on consistency and clarity, ensuring that every element has a purpose and contributes to a cohesive design.</p>
                        <div class="mt-4 pt-4 border-t border-slate-200 flex justify-around">
                            <button class="flex items-center gap-2 text-slate-500 hover:text-red-500 font-medium transition-colors">
                                <i data-lucide="heart" class="w-5 h-5"></i> 125
                            </button>
                             <button class="comment-btn flex items-center gap-2 text-slate-500 hover:text-sky-500 font-medium transition-colors">
                                <i data-lucide="message-circle" class="w-5 h-5"></i> 32
                            </button>
                             <button class="flex items-center gap-2 text-slate-500 hover:text-emerald-500 font-medium transition-colors">
                                <i data-lucide="repeat-2" class="w-5 h-5"></i> 12
                            </button>
                             <button class="flex items-center gap-2 text-slate-500 hover:text-indigo-500 font-medium transition-colors">
                                <i data-lucide="bookmark" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </article>
                    
                     <!-- Another Story Card -->
                    <article class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm" data-story-title="Finding Inspiration in Unexpected Places" data-story-content="Today I was walking through the city and saw something that sparked an idea for my next project. It's amazing how inspiration can strike when you least expect it. It's a reminder to always keep your eyes open to the world around you.">
                        <div class="flex items-center mb-4">
                            <img src="https://placehold.co/48x48/FBCFE8/9D174D?text=MS" alt="Maya S" class="w-12 h-12 rounded-full">
                            <div class="ml-4">
                                <h3 class="font-bold text-slate-900">Maya S.</h3>
                                <p class="text-sm text-slate-500">2 hours ago &middot; <span class="font-semibold text-amber-600">Inspiration</span></p>
                            </div>
                        </div>
                        <h2 class="text-2xl font-bold mb-2">Finding Inspiration in Unexpected Places</h2>
                        <p class="text-slate-600 leading-relaxed">Today I was walking through the city and saw something that sparked an idea for my next project. It's amazing how inspiration can strike when you least expect it. It's a reminder to always keep your eyes open to the world around you.</p>
                        <div class="mt-4 pt-4 border-t border-slate-200 flex justify-around">
                            <button class="flex items-center gap-2 text-red-500 font-medium transition-colors">
                                <i data-lucide="heart" class="w-5 h-5 fill-current"></i> 488
                            </button>
                             <button class="comment-btn flex items-center gap-2 text-slate-500 hover:text-sky-500 font-medium transition-colors">
                                <i data-lucide="message-circle" class="w-5 h-5"></i> 78
                            </button>
                             <button class="flex items-center gap-2 text-slate-500 hover:text-emerald-500 font-medium transition-colors">
                                <i data-lucide="repeat-2" class="w-5 h-5"></i> 41
                            </button>
                             <button class="flex items-center gap-2 text-slate-500 hover:text-indigo-500 font-medium transition-colors">
                                <i data-lucide="bookmark" class="w-5 h-5"></i>
                            </button>
                        </div>
                    </article>

                </div>

                <!-- Right Column (Widgets) -->
                <aside class="hidden lg:block lg:col-span-3 space-y-6">
                    <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <h3 class="font-bold text-lg mb-4">People You May Know</h3>
                        <div class="space-y-4">
                            <div class="flex items-center gap-3" data-person-name="Dr. Angela Brown" data-person-role="Educator">
                                <img src="https://placehold.co/40x40/A5B4FC/312E81?text=AB" alt="Angela Brown" class="w-10 h-10 rounded-full">
                                <div class="flex-grow">
                                    <p class="font-semibold">Dr. Angela Brown</p>
                                    <p class="text-sm text-slate-500 bio-text">Educator</p>
                                </div>
                                <button class="generate-bio-btn bg-slate-100 text-slate-600 font-semibold p-2 rounded-full text-sm hover:bg-slate-200 transition" title="Generate Bio">✨</button>
                            </div>
                             <div class="flex items-center gap-3" data-person-name="James Wilson" data-person-role="Designer with 3 mutual connections">
                                <img src="https://placehold.co/40x40/BEF264/365314?text=JW" alt="James Wilson" class="w-10 h-10 rounded-full">
                                <div class="flex-grow">
                                    <p class="font-semibold">James Wilson</p>
                                    <p class="text-sm text-slate-500 bio-text">3 mutual connections</p>
                                </div>
                                <button class="generate-bio-btn bg-slate-100 text-slate-600 font-semibold p-2 rounded-full text-sm hover:bg-slate-200 transition" title="Generate Bio">✨</button>
                            </div>
                        </div>
                    </div>
                     <div class="bg-white p-5 rounded-xl border border-slate-200 shadow-sm">
                        <h3 class="font-bold text-lg mb-4">Recent Activity</h3>
                        <ul class="space-y-3 text-sm">
                            <li class="flex items-start gap-3">
                                <i data-lucide="message-square-plus" class="text-sky-500 mt-1 flex-shrink-0"></i>
                                <p class="text-slate-600"><span class="font-semibold">Alicia</span> commented on "Community Building". <span class="text-slate-400 block">4h ago</span></p>
                            </li>
                            <li class="flex items-start gap-3">
                                <i data-lucide="heart" class="text-red-500 mt-1 flex-shrink-0"></i>
                                <p class="text-slate-600">You reached <span class="font-semibold">50 Hearts</span> on your story. <span class="text-slate-400 block">1d ago</span></p>
                            </li>
                             <li class="flex items-start gap-3">
                                <i data-lucide="user-plus" class="text-emerald-500 mt-1 flex-shrink-0"></i>
                                <p class="text-slate-600"><span class="font-semibold">Sarah</span> joined Creative Arts. <span class="text-slate-400 block">5h ago</span></p>
                            </li>
                        </ul>
                    </div>
                </aside>

            </div>
        </main>
    </div>

    <!-- Comment Modal -->
    <div id="commentModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-30 flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl w-full max-w-lg">
            <div class="p-5 border-b border-slate-200 flex justify-between items-center">
                <h3 class="text-lg font-bold">Leave a comment</h3>
                <button id="closeModalBtn" class="p-1 rounded-full hover:bg-slate-100">
                    <i data-lucide="x" class="w-5 h-5 text-slate-500"></i>
                </button>
            </div>
            <div class="p-5 space-y-4">
                <p class="text-sm text-slate-600">Commenting on: <span id="modalStoryTitle" class="font-semibold"></span></p>
                <textarea id="commentTextarea" class="w-full border-slate-300 rounded-lg focus:ring-indigo-500 focus:border-indigo-500 p-2 placeholder-slate-400" rows="4" placeholder="Share your thoughts..."></textarea>
                <div id="commentError" class="text-sm text-red-500"></div>
                <div class="flex justify-between items-center">
                    <button id="generateCommentBtn" class="flex items-center gap-1.5 text-sm font-semibold text-indigo-600 hover:text-indigo-800">✨ Generate Comment</button>
                    <button class="bg-indigo-600 text-white font-semibold px-5 py-2 rounded-full hover:bg-indigo-700 transition-colors duration-300">Post Comment</button>
                </div>
            </div>
        </div>
    </div>


    <script>
        lucide.createIcons();

        document.addEventListener('DOMContentLoaded', () => {
            // --- Gemini API Key ---
            const API_KEY = ""; 

            // --- ✨ AI Story Title Suggester ---
            const storyContentInput = document.getElementById('storyContentInput');
            const suggestTitlesBtn = document.getElementById('suggestTitlesBtn');
            const titleSuggestionsContainer = document.getElementById('titleSuggestionsContainer');
            const storyTitleInput = document.getElementById('storyTitleInput');

            storyContentInput.addEventListener('input', () => {
                if (storyContentInput.value.trim().length > 20) {
                    suggestTitlesBtn.classList.remove('hidden');
                    suggestTitlesBtn.classList.add('flex');
                } else {
                    suggestTitlesBtn.classList.add('hidden');
                    suggestTitlesBtn.classList.remove('flex');
                }
            });

            suggestTitlesBtn.addEventListener('click', async () => {
                const storyText = storyContentInput.value;
                if (storyText.length < 20) return;

                suggestTitlesBtn.disabled = true;
                suggestTitlesBtn.innerHTML = '<div class="spinner"></div>';
                titleSuggestionsContainer.classList.remove('hidden');
                titleSuggestionsContainer.innerHTML = '<p class="text-sm text-slate-500">✨ Thinking of some titles...</p>';

                const prompt = `Based on the following story draft, generate 5 creative and engaging title suggestions. Return as a JSON object with a "titles" array. Story: "${storyText}"`;
                
                const payload = {
                  contents: [{ role: "user", parts: [{ text: prompt }] }],
                  generationConfig: {
                    responseMimeType: "application/json",
                    responseSchema: { type: "OBJECT", properties: { titles: { type: "ARRAY", items: { type: "STRING" } } } }
                  }
                };

                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    const parsedJson = JSON.parse(result.candidates[0].content.parts[0].text);
                    
                    titleSuggestionsContainer.innerHTML = '';
                    parsedJson.titles.forEach(title => {
                        const btn = document.createElement('button');
                        btn.className = 'w-full text-left p-2 bg-slate-100 rounded-md hover:bg-indigo-100 text-slate-700 text-sm';
                        btn.textContent = title;
                        btn.onclick = () => {
                            storyTitleInput.value = title;
                            titleSuggestionsContainer.classList.add('hidden');
                        };
                        titleSuggestionsContainer.appendChild(btn);
                    });

                } catch (error) {
                    console.error("Error fetching title suggestions:", error);
                    titleSuggestionsContainer.innerHTML = `<p class="text-sm text-red-500">Could not fetch suggestions. ${error.message}</p>`;
                } finally {
                    suggestTitlesBtn.disabled = false;
                    suggestTitlesBtn.innerHTML = '✨ Suggest Titles';
                }
            });

            // --- ✨ AI Bio Generator ---
            document.querySelectorAll('.generate-bio-btn').forEach(button => {
                button.addEventListener('click', async (e) => {
                    const card = e.currentTarget.closest('[data-person-name]');
                    const name = card.dataset.personName;
                    const role = card.dataset.personRole;
                    const bioTextElement = card.querySelector('.bio-text');
                    
                    const originalText = bioTextElement.textContent;
                    button.disabled = true;
                    button.innerHTML = '<div class="spinner !w-5 !h-5"></div>';

                    const prompt = `Generate a short, one-sentence, friendly, and hypothetical bio for a person named ${name} who is a ${role}. Make it sound like a profile summary on a social network.`;
                    const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                    try {
                         const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify(payload)
                        });
                        if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                        const result = await response.json();
                        bioTextElement.textContent = result.candidates[0].content.parts[0].text;
                        button.classList.add('hidden');

                    } catch (error) {
                        console.error("Error generating bio:", error);
                        bioTextElement.textContent = originalText;
                        button.disabled = false;
                        button.innerHTML = '✨';
                    }
                });
            });

            // --- ✨ AI Comment Generator ---
            const modal = document.getElementById('commentModal');
            const closeModalBtn = document.getElementById('closeModalBtn');
            const modalStoryTitle = document.getElementById('modalStoryTitle');
            const commentTextarea = document.getElementById('commentTextarea');
            const generateCommentBtn = document.getElementById('generateCommentBtn');
            const commentError = document.getElementById('commentError');

            document.querySelectorAll('.comment-btn').forEach(button => {
                button.addEventListener('click', (e) => {
                    const article = e.currentTarget.closest('article');
                    const title = article.dataset.storyTitle;
                    const content = article.dataset.storyContent;
                    
                    modalStoryTitle.textContent = title;
                    modal.dataset.storyTitle = title;
                    modal.dataset.storyContent = content;
                    commentTextarea.value = '';
                    commentError.textContent = '';
                    modal.classList.remove('hidden');
                });
            });

            const closeModal = () => modal.classList.add('hidden');
            closeModalBtn.addEventListener('click', closeModal);
            modal.addEventListener('click', (e) => {
                if (e.target === modal) closeModal();
            });

            generateCommentBtn.addEventListener('click', async () => {
                const title = modal.dataset.storyTitle;
                const content = modal.dataset.storyContent;

                generateCommentBtn.disabled = true;
                generateCommentBtn.innerHTML = '<div class="spinner"></div> Generating...';
                commentError.textContent = '';

                const prompt = `Generate a short, insightful, and friendly comment for a story titled "${title}". The story content is: "${content}"`;
                const payload = { contents: [{ role: "user", parts: [{ text: prompt }] }] };

                try {
                    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${API_KEY}`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(payload)
                    });
                    if (!response.ok) throw new Error(`API call failed: ${response.status}`);
                    const result = await response.json();
                    commentTextarea.value = result.candidates[0].content.parts[0].text;
                } catch (error) {
                    console.error("Error generating comment:", error);
                    commentError.textContent = `Failed to generate comment. ${error.message}`;
                } finally {
                    generateCommentBtn.disabled = false;
                    generateCommentBtn.innerHTML = '✨ Generate Comment';
                }
            });
        });
    </script>
</body>
</html>
