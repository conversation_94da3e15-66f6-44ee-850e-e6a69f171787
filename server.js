const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const fs = require('fs');

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// Serve static files (your existing HTML, CSS, JS)
app.use(express.static('.'));

// Serve your main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// Data storage files
const USERS_FILE = 'users.json';
const POSTS_FILE = 'posts.json';

// Initialize data files if they don't exist
function initializeDataFiles() {
    if (!fs.existsSync(USERS_FILE)) {
        fs.writeFileSync(USERS_FILE, JSON.stringify([]));
    }
    if (!fs.existsSync(POSTS_FILE)) {
        fs.writeFileSync(POSTS_FILE, JSON.stringify([]));
    }
}

// Helper functions to read/write data
function readUsers() {
    try {
        const data = fs.readFileSync(USERS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

function writeUsers(users) {
    fs.writeFileSync(USERS_FILE, JSON.stringify(users, null, 2));
}

function readPosts() {
    try {
        const data = fs.readFileSync(POSTS_FILE, 'utf8');
        return JSON.parse(data);
    } catch (error) {
        return [];
    }
}

function writePosts(posts) {
    fs.writeFileSync(POSTS_FILE, JSON.stringify(posts, null, 2));
}

// API Routes

// Like a post
app.post('/api/posts/:id/like', (req, res) => {
    const postId = req.params.id;
    const { userId } = req.body;

    if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
    }

    const posts = readPosts();
    const postIndex = posts.findIndex(p => p.id === postId);

    if (postIndex === -1) {
        return res.status(404).json({ error: 'Post not found' });
    }

    // Initialize likes array if it doesn't exist
    if (!posts[postIndex].likedBy) {
        posts[postIndex].likedBy = [];
    }

    // Check if user already liked the post
    const alreadyLiked = posts[postIndex].likedBy.includes(userId);

    if (alreadyLiked) {
        // Unlike the post
        posts[postIndex].likedBy = posts[postIndex].likedBy.filter(id => id !== userId);
        posts[postIndex].likes = Math.max(0, (posts[postIndex].likes || 0) - 1);
    } else {
        // Like the post
        posts[postIndex].likedBy.push(userId);
        posts[postIndex].likes = (posts[postIndex].likes || 0) + 1;
    }

    writePosts(posts);

    res.json({
        success: true,
        liked: !alreadyLiked,
        likes: posts[postIndex].likes
    });
});

// Add a comment to a post
app.post('/api/posts/:id/comment', (req, res) => {
    const postId = req.params.id;
    const { userId, username, content } = req.body;

    if (!userId || !username || !content) {
        return res.status(400).json({ error: 'All fields are required' });
    }

    const posts = readPosts();
    const postIndex = posts.findIndex(p => p.id === postId);

    if (postIndex === -1) {
        return res.status(404).json({ error: 'Post not found' });
    }

    // Initialize comments array if it doesn't exist
    if (!posts[postIndex].commentsList) {
        posts[postIndex].commentsList = [];
    }

    const newComment = {
        id: Date.now().toString(),
        userId,
        username,
        content,
        createdAt: new Date().toISOString()
    };

    posts[postIndex].commentsList.push(newComment);
    posts[postIndex].comments = posts[postIndex].commentsList.length;

    writePosts(posts);

    res.json({
        success: true,
        comment: newComment,
        comments: posts[postIndex].comments
    });
});

// Share a post
app.post('/api/posts/:id/share', (req, res) => {
    const postId = req.params.id;
    const { userId } = req.body;

    if (!userId) {
        return res.status(400).json({ error: 'User ID is required' });
    }

    const posts = readPosts();
    const postIndex = posts.findIndex(p => p.id === postId);

    if (postIndex === -1) {
        return res.status(404).json({ error: 'Post not found' });
    }

    // Initialize shares array if it doesn't exist
    if (!posts[postIndex].sharedBy) {
        posts[postIndex].sharedBy = [];
    }

    // Check if user already shared the post
    const alreadyShared = posts[postIndex].sharedBy.includes(userId);

    if (!alreadyShared) {
        posts[postIndex].sharedBy.push(userId);
        posts[postIndex].shares = (posts[postIndex].shares || 0) + 1;

        writePosts(posts);
    }

    res.json({
        success: true,
        shares: posts[postIndex].shares
    });
});

// User registration
app.post('/api/register', (req, res) => {
    const { username, email, password } = req.body;
    
    if (!username || !email || !password) {
        return res.status(400).json({ error: 'All fields are required' });
    }
    
    const users = readUsers();
    
    // Check if user already exists
    if (users.find(user => user.email === email || user.username === username)) {
        return res.status(400).json({ error: 'User already exists' });
    }
    
    // Create new user
    const newUser = {
        id: Date.now().toString(),
        username,
        email,
        password, // In production, this should be hashed!
        createdAt: new Date().toISOString(),
        bio: "New to the community",
        followers: 0,
        following: 0,
        stories: 0
    };
    
    users.push(newUser);
    writeUsers(users);
    
    // Don't send password back
    const { password: _, ...userResponse } = newUser;
    res.json({ success: true, user: userResponse });
});

// User login
app.post('/api/login', (req, res) => {
    const { email, password } = req.body;
    
    if (!email || !password) {
        return res.status(400).json({ error: 'Email and password are required' });
    }
    
    const users = readUsers();
    const user = users.find(u => u.email === email && u.password === password);
    
    if (!user) {
        return res.status(401).json({ error: 'Invalid credentials' });
    }
    
    // Don't send password back
    const { password: _, ...userResponse } = user;
    res.json({ success: true, user: userResponse });
});

// Update user profile
app.put('/api/users/:id', (req, res) => {
    const userId = req.params.id;
    const { username, bio } = req.body;

    if (!username && !bio) {
        return res.status(400).json({ error: 'At least one field (username or bio) is required' });
    }

    const users = readUsers();
    const userIndex = users.findIndex(u => u.id === userId);

    if (userIndex === -1) {
        return res.status(404).json({ error: 'User not found' });
    }

    // Update user fields
    if (username) {
        // Check if username is already taken by another user
        const existingUser = users.find(u => u.username === username && u.id !== userId);
        if (existingUser) {
            return res.status(400).json({ error: 'Username already taken' });
        }
        users[userIndex].username = username;
    }

    if (bio !== undefined) {
        users[userIndex].bio = bio;
    }

    writeUsers(users);

    // Don't send password back
    const { password: _, ...userResponse } = users[userIndex];
    res.json({ success: true, user: userResponse });
});

// Get all posts
app.get('/api/posts', (req, res) => {
    const posts = readPosts();
    res.json(posts);
});

// Create new post
app.post('/api/posts', (req, res) => {
    const { title, content, userId, username } = req.body;
    
    if (!title || !content || !userId || !username) {
        return res.status(400).json({ error: 'All fields are required' });
    }
    
    const posts = readPosts();
    
    const newPost = {
        id: Date.now().toString(),
        title,
        content,
        userId,
        username,
        createdAt: new Date().toISOString(),
        likes: 0,
        comments: 0,
        shares: 0,
        category: "Personal Experience"
    };
    
    posts.unshift(newPost); // Add to beginning of array
    writePosts(posts);
    
    // Update user's story count
    const users = readUsers();
    const userIndex = users.findIndex(u => u.id === userId);
    if (userIndex !== -1) {
        users[userIndex].stories += 1;
        writeUsers(users);
    }
    
    res.json({ success: true, post: newPost });
});

// Initialize data files
initializeDataFiles();

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Narrate server running on http://localhost:${PORT}`);
    console.log('📱 Your social media platform is ready!');
});
